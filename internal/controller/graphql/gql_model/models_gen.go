// Code generated by github.com/99designs/gqlgen, DO NOT EDIT.

package gql_model

import (
	"bytes"
	"fmt"
	"io"
	"strconv"
	"time"
)

type CreateUserInput struct {
	Email          string  `json:"email"`
	InvitationCode *string `json:"invitationCode,omitempty"`
	ReferrerCode   *string `json:"referrerCode,omitempty"`
}

type CreateUserInvitationCodeInput struct {
	UserID           string     `json:"userId"`
	InvitationCode   string     `json:"invitationCode"`
	Email            *string    `json:"email,omitempty"`
	IsFirstLogin     *bool      `json:"isFirstLogin,omitempty"`
	IsExportedWallet *bool      `json:"isExportedWallet,omitempty"`
	Chain            *string    `json:"chain,omitempty"`
	Name             *string    `json:"name,omitempty"`
	WalletAddress    *string    `json:"walletAddress,omitempty"`
	WalletID         *string    `json:"walletId,omitempty"`
	WalletAccountID  *string    `json:"walletAccountId,omitempty"`
	WalletType       WalletType `json:"walletType"`
}

type CreateUserResponse struct {
	User    *User  `json:"user"`
	Success bool   `json:"success"`
	Message string `json:"message"`
}

type CreateUserWalletInput struct {
	UserID          string     `json:"userId"`
	Chain           string     `json:"chain"`
	Name            *string    `json:"name,omitempty"`
	WalletAddress   string     `json:"walletAddress"`
	WalletID        *string    `json:"walletId,omitempty"`
	WalletAccountID *string    `json:"walletAccountId,omitempty"`
	WalletType      WalletType `json:"walletType"`
}

type CreateUserWalletResponse struct {
	Wallet  *UserWallet `json:"wallet"`
	Success bool        `json:"success"`
	Message string      `json:"message"`
}

type CreateUserWithReferralInput struct {
	UserID         string `json:"userId"`
	InvitationCode string `json:"invitationCode"`
}

type GenerateInvitationCodeResponse struct {
	User    *User  `json:"user"`
	Success bool   `json:"success"`
	Message string `json:"message"`
}

type Mutation struct {
}

type Query struct {
}

type Referral struct {
	ID         int       `json:"id"`
	UserID     string    `json:"userId"`
	ReferrerID *string   `json:"referrerId,omitempty"`
	Depth      int       `json:"depth"`
	CreatedAt  time.Time `json:"createdAt"`
	User       *User     `json:"user"`
	Referrer   *User     `json:"referrer,omitempty"`
}

type ReferralSnapshot struct {
	UserID                  string  `json:"userId"`
	DirectCount             int     `json:"directCount"`
	TotalDownlineCount      int     `json:"totalDownlineCount"`
	TotalVolumeUsd          float64 `json:"totalVolumeUsd"`
	TotalRewardsDistributed float64 `json:"totalRewardsDistributed"`
	User                    *User   `json:"user"`
}

type UpdateInvitationCodeInput struct {
	UserID         string `json:"userId"`
	InvitationCode string `json:"invitationCode"`
}

type UpdateInvitationCodeResponse struct {
	User    *User  `json:"user"`
	Success bool   `json:"success"`
	Message string `json:"message"`
}

type User struct {
	ID               string            `json:"id"`
	Email            *string           `json:"email,omitempty"`
	IsFirstLogin     bool              `json:"isFirstLogin"`
	IsExportedWallet bool              `json:"isExportedWallet"`
	InvitationCode   *string           `json:"invitationCode,omitempty"`
	CreatedAt        time.Time         `json:"createdAt"`
	UpdatedAt        time.Time         `json:"updatedAt"`
	Wallets          []*UserWallet     `json:"wallets"`
	Referral         *Referral         `json:"referral,omitempty"`
	ReferralSnapshot *ReferralSnapshot `json:"referralSnapshot,omitempty"`
	Referrals        []*Referral       `json:"referrals"`
}

type UserWallet struct {
	ID              string      `json:"id"`
	UserID          string      `json:"userId"`
	Chain           string      `json:"chain"`
	Name            *string     `json:"name,omitempty"`
	WalletAddress   string      `json:"walletAddress"`
	WalletID        *string     `json:"walletId,omitempty"`
	WalletAccountID *string     `json:"walletAccountId,omitempty"`
	WalletType      *WalletType `json:"walletType,omitempty"`
	CreatedAt       time.Time   `json:"createdAt"`
	UpdatedAt       time.Time   `json:"updatedAt"`
	User            *User       `json:"user"`
}

type WalletType string

const (
	WalletTypeEmbedded WalletType = "EMBEDDED"
	WalletTypeManaged  WalletType = "MANAGED"
)

var AllWalletType = []WalletType{
	WalletTypeEmbedded,
	WalletTypeManaged,
}

func (e WalletType) IsValid() bool {
	switch e {
	case WalletTypeEmbedded, WalletTypeManaged:
		return true
	}
	return false
}

func (e WalletType) String() string {
	return string(e)
}

func (e *WalletType) UnmarshalGQL(v any) error {
	str, ok := v.(string)
	if !ok {
		return fmt.Errorf("enums must be strings")
	}

	*e = WalletType(str)
	if !e.IsValid() {
		return fmt.Errorf("%s is not a valid WalletType", str)
	}
	return nil
}

func (e WalletType) MarshalGQL(w io.Writer) {
	fmt.Fprint(w, strconv.Quote(e.String()))
}

func (e *WalletType) UnmarshalJSON(b []byte) error {
	s, err := strconv.Unquote(string(b))
	if err != nil {
		return err
	}
	return e.UnmarshalGQL(s)
}

func (e WalletType) MarshalJSON() ([]byte, error) {
	var buf bytes.Buffer
	e.MarshalGQL(&buf)
	return buf.Bytes(), nil
}
